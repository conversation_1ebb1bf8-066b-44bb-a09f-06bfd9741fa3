# Installation
> `npm install --save @types/ioredis`

# Summary
This package contains type definitions for ioredis (https://github.com/luin/ioredis).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ioredis.

### Additional Details
 * Last updated: Sat, 19 Mar 2022 03:31:42 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [<PERSON>](https://github.com/plantain-00), [<PERSON>](https://github.com/chrisleck), [<PERSON>](https://github.com/aliarham11), [<PERSON><PERSON><PERSON>](https://github.com/br8h), [<PERSON><PERSON><PERSON>](https://github.com/palindrom615), [<PERSON>](https://github.com/funthing), [<PERSON>g <PERSON>](https://github.com/iamolegga), [<PERSON><PERSON><PERSON><PERSON><PERSON>](https://github.com/tingwai-to), [<PERSON>](https://github.com/pettyalex), [<PERSON>](https://github.com/Simon<PERSON>chi<PERSON>), [Tianlin](https://github.com/tianlinle), [Demian <PERSON>](https://github.com/demian85), [Andrew Lavers](https://github.com/alavers), [Claudiu Ceia](https://github.com/ClaudiuCeia), [Asyrique](https://github.com/asyrique), [Michael Salaverry](https://github.com/barakplasma), [Hannes Van De Vreken](https://github.com/hannesvdvreken), [T.J. Tarazevits](https://github.com/venku122), [Michiel De Mey](https://github.com/michieldemey), [Dae Heon Han](https://github.com/honeyirene), and [Yongkyun Choi](https://github.com/DracoVirus).
