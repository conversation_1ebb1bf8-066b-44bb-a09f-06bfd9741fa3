{"name": "@types/qs", "version": "6.9.18", "description": "TypeScript definitions for qs", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>", "url": "https://github.com/RWander"}, {"name": "<PERSON>", "githubUsername": "leonyu", "url": "https://github.com/leonyu"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tehbelinda"}, {"name": "<PERSON>", "githubUsername": "zyml", "url": "https://github.com/zyml"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/artursvonda"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "dpsmith3", "url": "https://github.com/dpsmith3"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin", "url": "https://github.com/hperrin"}, {"name": "<PERSON>", "githubUsername": "lj<PERSON>b", "url": "https://github.com/ljharb"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/qs"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "2b3ee00ab119740c8d250fb641a3ab35b4a40a86c735d92f5d6ca12894a1ec0f", "typeScriptVersion": "5.0"}