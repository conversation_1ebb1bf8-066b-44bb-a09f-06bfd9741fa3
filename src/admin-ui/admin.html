<!--
  Chat Relay: Relay for AI Chat Interfaces
  Copyright (C) 2025 <PERSON>ison Moore

  This program is free software: you can redistribute it and/or modify
  it under the terms of the GNU Affero General Public License as
  published by the Free Software Foundation, either version 3 of the
  License, or (at your option) any later version.

  This program is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU Affero General Public License for more details.

  You should have received a copy of the GNU Affero General Public License
  along with this program.  If not, see https://www.gnu.org/licenses/.
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Relay Admin</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            color: #1c1e21;
            line-height: 1.6;
        }
        header {
            background: linear-gradient(135deg, #1877f2 0%, #4285f4 100%);
            color: #fff;
            padding: 1.5em;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        .header-content {
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .chat-bubble {
            width: 40px;
            height: 40px;
            background-color: #fff;
            border-radius: 50%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .chat-bubble::before {
            content: '';
            width: 24px;
            height: 24px;
            background-color: #1877f2;
            border-radius: 50%;
            position: relative;
        }
        .chat-bubble::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid #fff;
        }
        header h1 {
            margin: 0;
            font-size: 2em;
            font-weight: 300;
        }
        nav {
            background-color: #fff;
            padding: 0;
            border-bottom: 1px solid #dddfe2;
            box-shadow: 0 2px 2px -2px rgba(0,0,0,0.05);
        }
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2em;
        }
        nav ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            display: flex;
            gap: 0;
        }
        nav ul li {
            margin: 0;
        }
        nav ul li a {
            color: #65676b;
            text-decoration: none;
            font-weight: 500;
            padding: 1em 1.5em;
            display: block;
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
        }
        nav ul li a:hover {
            color: #1c1e21;
            background-color: #f8f9fa;
        }
        nav ul li a.active {
            color: #1c1e21;
            border-bottom: 3px solid #1877f2;
            font-weight: 600;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2em;
            box-sizing: border-box;
        }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        
        /* Settings Layout */
        .settings-main {
            background-color: #f8f9fa;
            min-height: calc(100vh - 200px);
            padding: 2em 0;
        }
        .settings-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 1.5em;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2em;
        }
        .settings-form {
            display: flex;
            flex-direction: column;
            gap: 1.5em;
        }
        .form-section {
            background-color: #fff;
            border-radius: 12px;
            padding: 1.5em;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .form-section h2 {
            margin: 0 0 1em 0;
            font-size: 1.25em;
            font-weight: 600;
            color: #1c1e21;
            border: none;
            padding: 0;
        }
        .form-group {
            margin-bottom: 1em;
        }
        .form-group:last-child {
            margin-bottom: 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5em;
            font-weight: 500;
            color: #1c1e21;
        }
        .form-group input[type="number"],
        .form-group input[type="text"] {
            width: 100%;
            padding: 0.75em;
            border: 1px solid #dddfe2;
            border-radius: 6px;
            font-size: 0.95em;
            transition: border-color 0.2s ease;
            box-sizing: border-box;
        }
        .form-group input[type="number"]:focus,
        .form-group input[type="text"]:focus {
            outline: none;
            border-color: #1877f2;
            box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.2);
        }
        .radio-group {
            display: flex;
            gap: 1em;
            margin-top: 0.5em;
        }
        .radio-option {
            display: flex;
            align-items: center;
            gap: 0.5em;
            padding: 0.5em 1em;
            border: 1px solid #dddfe2;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .radio-option:hover {
            background-color: #f8f9fa;
        }
        .radio-option input[type="radio"] {
            margin: 0;
        }
        .radio-option input[type="radio"]:checked + span {
            font-weight: 600;
        }
        .toggle-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            display: inline-block;
        }
        .toggle-switch input[type="checkbox"] {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.3s;
            border-radius: 24px;
            z-index: 1;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
            z-index: 2;
        }
        .toggle-switch input[type="checkbox"]:checked + .slider {
            background-color: #1877f2;
        }
        .toggle-switch input[type="checkbox"]:checked + .slider:before {
            transform: translateX(26px);
        }
        .toggle-switch:hover .slider {
            box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.2);
        }
        
        /* Logs Panel */
        .logs-panel {
            background-color: #2d3748;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .logs-header {
            background-color: #4a5568;
            padding: 1em 1.5em;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #fff;
        }
        .logs-header h3 {
            margin: 0;
            font-size: 1.1em;
            font-weight: 600;
        }
        .expand-icon {
            font-size: 1.2em;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        .logs-content {
            padding: 1em;
            height: 60vh;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.85em;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 0.5em;
            padding: 0.25em 0;
        }
        .log-entry.info {
            color: #68d391;
        }
        .log-entry.warn {
            color: #fbb040;
        }
        .log-entry.error {
            color: #f56565;
        }
        
        /* Form Actions */
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1em;
            max-width: 1400px;
            margin: 1em auto 0 auto;
            padding: 0 2em 2em 2em;
        }
        .btn {
            padding: 0.75em 2em;
            border: none;
            border-radius: 6px;
            font-size: 0.95em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .btn-primary {
            background-color: #28a745;
            color: white;
        }
        .btn-primary:hover {
            background-color: #218838;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        
        h2 {
            border-bottom: 2px solid #dddfe2;
            padding-bottom: 0.5em;
            color: #1c1e21;
            font-size: 1.5em;
            margin-top: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1.5em;
            table-layout: fixed; /* Important for controlling column widths */
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
            border-radius: 6px;
            overflow: hidden; /* Ensures border-radius is respected by children */
        }
        th, td {
            border: 1px solid #dddfe2;
            padding: 12px 10px; /* Increased padding */
            text-align: left;
            vertical-align: top; /* Align content to the top */
            word-wrap: break-word; /* Prevent long words from breaking layout */
        }
        th {
            background-color: #f5f6f7; /* Lighter header background */
            color: #4b4f56; /* Darker gray for header text */
            font-weight: 600;
            position: relative; /* For positioning collapse buttons */
        }
        /* Column width styling */
        th.col-timestamp, td.col-timestamp { width: 12%; } /* Start/End Timestamp */
        th.col-request-id, td.col-request-id { width: 8%; }  /* Request ID */
        th.col-status, td.col-status { width: 8%; }        /* Status */
        /* The remaining 4 data columns will share the rest of the space.
           (100 - 12 - 12 - 8 - 8) = 60%. So each gets 15% */
        th.col-data, td.col-data { width: 15%; }

        td pre { /* Style for the <pre> tags inside cells */
            margin: 0;
            white-space: pre-wrap;
            max-height: 200px; /* Keep existing max-height */
            overflow-y: auto;
            background-color: #f9f9f9; /* Slight background for pre blocks */
            padding: 6px;
            border-radius: 4px;
            font-size: 0.9em;
            color: #333;
        }

        .log-window {
            background-color: #1e1e1e; /* Darker background for log */
            color: #d4d4d4; /* Lighter text for log */
            font-family: 'Consolas', 'Courier New', Courier, monospace;
            padding: 15px;
            height: 250px; /* Slightly taller */
            overflow-y: scroll;
            border: 1px solid #333;
            margin-top: 1.5em;
            border-radius: 4px;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
        }
        .log-entry {
            white-space: pre-wrap;
            padding: 2px 0;
        }
        .collapsible-header {
            background-color: #6c757d; /* Bootstrap secondary color */
            color: white;
            padding: 0.75em;
            cursor: pointer;
            text-align: center;
            border-radius: 4px;
            margin-top: 1em;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }
        .collapsible-header:hover {
            background-color: #5a6268;
        }
        button {
            padding: 8px 15px;
            font-size: 0.9em;
            border-radius: 4px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: background-color 0.2s ease, border-color 0.2s ease;
        }
        #refresh-messages-btn {
            background-color: #007bff; /* Primary blue */
            color: white;
            margin-left: 10px;
        }
        #refresh-messages-btn:hover {
            background-color: #0056b3;
        }
        #save-settings-btn {
            background-color: #28a745; /* Green for save */
            color: white;
        }
        #save-settings-btn:hover {
            background-color: #1e7e34;
        }
        #restart-server-btn {
            background-color: #dc3545; /* Red for restart/danger */
            color: white;
        }
        #restart-server-btn:hover {
            background-color: #c82333;
        }
        input[type="text"], input[type="number"], select {
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9em;
        }
        label {
            font-weight: 500;
            margin-right: 5px;
        }
        .settings-item, .status-item {
            margin-bottom: 1em;
            padding: 10px;
            background-color: #fff;
            border: 1px solid #dddfe2;
            border-radius: 4px;
        }
        #update-status-msg {
            margin-left: 10px;
            font-style: italic;
        }
        .collapse-btn {
            font-size: 0.7em;
            padding: 2px 5px;
            margin-left: 5px;
            cursor: pointer;
            border: 1px solid #ccc;
            background-color: #f0f0f0;
            border-radius: 3px;
            display: inline-block; /* To sit next to header text */
            vertical-align: middle;
        }
        .collapse-btn:hover {
            background-color: #e0e0e0;
        }
        .collapsed-cell {
            /* Could add specific styling for collapsed cells if needed, e.g., a placeholder */
            /* For now, they will just be hidden by JavaScript */
        }
        .header-collapsed {
            width: 1px !important; /* Minimal width to keep column in layout but visually gone */
            min-width: 1px !important; /* Ensure it doesn't expand due to content */
            padding-left: 0 !important;
            padding-right: 0 !important;
            border-left-width: 0 !important;
            border-right-width: 0 !important;
            overflow: hidden !important; /* Hide content that might overflow 1px */
            color: transparent !important; /* Hide text by making it transparent */
            font-size: 0 !important; /* Another way to hide text, affects children */
        }
        .header-collapsed .collapse-btn { /* Ensure button is also hidden if not already by font-size:0 */
            display: none !important;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">
                <div class="chat-bubble"></div>
                <h1>Chat Relay</h1>
            </div>
        </div>
    </header>
    <nav>
        <div class="nav-container">
            <ul>
                <li><a href="#" class="tab-link active" data-tab="messages">Messages</a></li>
                <li><a href="#" class="tab-link" data-tab="settings">Settings</a></li>
            </ul>
        </div>
    </nav>
    <div class="container">
        <div id="messages" class="tab-content active">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1em;">
                <h2>Message History</h2>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 15px;">Server Uptime: <span id="status-uptime">N/A</span></span>
                    <span style="margin-right: 15px;">Connected Extensions: <span id="status-connected-extensions">0</span></span>
                    <button id="refresh-messages-btn" style="font-size: 0.8em; padding: 0.5em 1em; margin-right: 10px;">Refresh Messages</button>
                    <button id="restart-server-btn" style="font-size: 0.8em; padding: 0.5em 1em; background-color: #d9534f; color: white; border: none; cursor: pointer;">Restart Server</button>
                </div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th class="col-timestamp">Start Timestamp</th>
                        <th class="col-timestamp">End Timestamp</th>
                        <th class="col-request-id">Request ID</th>
                        <th class="col-data">From Client <span class="collapse-btn" data-column="fromClient" title="Toggle Column">-</span></th>
                        <th class="col-data">To Extension <span class="collapse-btn" data-column="toExtension" title="Toggle Column">-</span></th>
                        <th class="col-data">From Extension <span class="collapse-btn" data-column="fromExtension" title="Toggle Column">-</span></th>
                        <th class="col-data">To Client <span class="collapse-btn" data-column="toClient" title="Toggle Column">-</span></th>
                        <th class="col-status">Status</th>
                    </tr>
                </thead>
                <tbody id="message-history-body">
                    <!-- Message rows will be inserted here by JavaScript -->
                </tbody>
            </table>
        </div>
        <div id="settings" class="tab-content">
            <main class="settings-main">
                <div class="settings-content">
                    <div class="settings-form">
                        <div class="form-section">
                            <h2>Server</h2>
                            <div class="form-group">
                                <label for="port-input">Server Port</label>
                                <input type="number" id="port-input" value="3003">
                            </div>
                            <div class="form-group">
                                <label for="request-timeout-input">Request Timeout</label>
                                <input type="number" id="request-timeout-input" value="300000">
                            </div>
                        </div>

                        <div class="form-section">
                            <h2>Request Handling</h2>
                            <div class="form-group">
                                <label>Queue</label>
                                <div class="radio-group">
                                    <label class="radio-option">
                                        <input type="radio" id="newRequestBehaviorQueue" name="newRequestBehavior" value="queue" checked>
                                        <span>Queue</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" id="newRequestBehaviorDrop" name="newRequestBehavior" value="drop">
                                        <span>Drop</span>
                                    </label>
                                </div>
                            </div>
                            <div class="form-group toggle-group">
                                <label for="auto-kill-port-input">Auto-kill conflicting process</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="auto-kill-port-input" checked>
                                    <span class="slider"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h2>Heartbeat</h2>
                            <div class="form-group">
                                <label for="setting-ping-interval-input">Ping interval (ms)</label>
                                <input type="text" id="setting-ping-interval-input" value="Disabled" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="logs-panel">
                        <div class="logs-header">
                            <h3>Logs</h3>
                            <span class="expand-icon">→</span>
                        </div>
                        <div class="logs-content" id="logs-content">
                            <div class="log-entry info">[INFO] Server started</div>
                            <div class="log-entry warn">[WARN] High memory usage</div>
                            <div class="log-entry error">[ERROR] Failed to connect</div>
                            <div class="log-entry error">[ERROR] Failed to connect</div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button class="btn btn-primary" id="save-settings-btn">Save</button>
                </div>
                <span id="update-status-msg" style="margin-left: 10px; font-style: italic;"></span>
            </main>
        </div>
        <!-- Status tab content removed as its elements are moved to the Messages tab -->
    </div>
    <div class="collapsible-header" onclick="toggleLogWindow()">Server Logs (click to toggle)</div>
    <div class="log-window" id="log-window-content" style="display: none;">
        <!-- Log entries will be inserted here -->
    </div>
    <script>
        // Basic tab switching
        const tabLinks = document.querySelectorAll('.tab-link');
        const tabContents = document.querySelectorAll('.tab-content');
        tabLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                tabLinks.forEach(l => l.classList.remove('active'));
                tabContents.forEach(tc => tc.classList.remove('active'));
                link.classList.add('active');
                const activeTabContent = document.getElementById(link.dataset.tab);
                activeTabContent.classList.add('active');

                // If settings tab is activated, refresh its content
                if (link.dataset.tab === 'settings') {
                    fetchAndDisplayServerInfo(); // This function now also updates status on the messages tab
                }
                 // Always ensure server info (which includes status) is fresh when messages tab is active
                if (link.dataset.tab === 'messages') {
                    fetchAndDisplayServerInfo();
                }
            });
        });
        
        // New logs panel functionality
        async function updateLogsPanel() {
            const logsContent = document.getElementById('logs-content');
            if (logsContent) {
                try {
                    // Fetch real logs from the server
                    const response = await fetch('/admin/logs');
                    if (response.ok) {
                        const logs = await response.json();
                        if (logs && logs.length > 0) {
                            // Display the most recent logs first
                            const recentLogs = logs.slice(-20).reverse();
                            logsContent.innerHTML = recentLogs.map(log => {
                                const timestamp = new Date(log.timestamp).toLocaleTimeString();
                                return `<div class="log-entry ${log.level}">[${timestamp}] ${log.message}</div>`;
                            }).join('');
                            // Auto-scroll to top to show newest logs
                            logsContent.scrollTop = 0;
                            return;
                        }
                    }
                } catch (error) {
                    console.error('Error fetching logs:', error);
                }
                
                // Show sample logs as fallback
                const sampleLogs = [
                    { type: 'info', message: '[INFO] Server started' },
                    { type: 'warn', message: '[WARN] High memory usage' },
                    { type: 'error', message: '[ERROR] Failed to connect' },
                    { type: 'error', message: '[ERROR] Failed to connect' }
                ];
                
                logsContent.innerHTML = sampleLogs.map(log =>
                    `<div class="log-entry ${log.type}">${log.message}</div>`
                ).join('');
            }
        }
        
        
        function toggleLogWindow() {
            const logWindow = document.getElementById('log-window-content');
            if (logWindow.style.display === 'none') {
                logWindow.style.display = 'block';
            } else {
                logWindow.style.display = 'none';
            }
        }
        const messageHistoryBody = document.getElementById('message-history-body');
        const refreshButton = document.getElementById('refresh-messages-btn');

        function createPreCell(data) {
            const cell = document.createElement('td');
            if (data === undefined || data === null) {
                cell.textContent = 'N/A';
            } else {
                const pre = document.createElement('pre');
                // pre.style.margin = '0'; // Handled by td pre CSS
                // pre.style.whiteSpace = 'pre-wrap'; // Handled by td pre CSS
                // pre.style.maxHeight = '200px'; // Handled by td pre CSS
                // pre.style.overflowY = 'auto';  // Handled by td pre CSS
                pre.textContent = JSON.stringify(data, null, 2);
                cell.appendChild(pre);
            }
            return cell;
        }

        async function fetchAndDisplayMessageHistory() {
            try {
                const response = await fetch('/v1/admin/message-history');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const messages = await response.json();

                messageHistoryBody.innerHTML = ''; // Clear existing rows

                if (messages.length === 0) {
                    const row = messageHistoryBody.insertRow();
                    const cell = row.insertCell();
                    cell.colSpan = 8; // Adjusted for new column
                    cell.textContent = 'No message history found.';
                    cell.style.textAlign = 'center';
                    return;
                }

                // Group messages by requestId
                const groupedMessages = messages.reduce((acc, logEntry) => {
                    const id = logEntry.requestId;
                    if (!acc[id]) {
                        acc[id] = {
                            requestId: id,
                            startTimestamp: logEntry.timestamp, // Default to first seen
                            endTimestamp: undefined, // Initialize endTimestamp
                            fromClient: undefined,
                            toExtension: undefined,
                            fromExtension: undefined,
                            toClient: undefined,
                            status: "Unknown"
                        };
                    }

                    // Update fields based on log type
                    switch (logEntry.type) {
                        case 'CHAT_REQUEST_RECEIVED':
                            // Ensure startTimestamp is the earliest one if multiple CHAT_REQUEST_RECEIVED logs existed (though unlikely for same ID)
                            if (!acc[id].startTimestamp || new Date(logEntry.timestamp) < new Date(acc[id].startTimestamp)) {
                                acc[id].startTimestamp = logEntry.timestamp;
                            }
                            acc[id].fromClient = logEntry.data.fromClient;
                            acc[id].toExtension = logEntry.data.toExtension;
                            if (acc[id].status === "Unknown" || acc[id].status === "Request In Progress") {
                                acc[id].status = "Request In Progress";
                            }
                            break;
                        case 'CHAT_RESPONSE_SENT':
                            acc[id].fromExtension = logEntry.data.fromExtension;
                            acc[id].toClient = logEntry.data.toClient;
                            acc[id].status = logEntry.data.status || "Success";
                            acc[id].endTimestamp = logEntry.timestamp; // Set end time on success
                            break;
                        case 'CHAT_ERROR_RESPONSE_SENT':
                            acc[id].toClient = logEntry.data.toClientError;
                            acc[id].status = logEntry.data.status || "Error";
                            acc[id].endTimestamp = logEntry.timestamp; // Set end time on error
                            break;
                    }
                    return acc;
                }, {});

                // Convert grouped messages object to an array and sort by timestamp (most recent first)
                const consolidatedMessages = Object.values(groupedMessages).sort((a, b) => {
                    // Sort by startTimestamp, most recent first
                    return new Date(b.startTimestamp) - new Date(a.startTimestamp);
                });

                if (consolidatedMessages.length === 0) {
                     const row = messageHistoryBody.insertRow();
                    const cell = row.insertCell();
                    cell.colSpan = 8; // Adjusted for new column
                    cell.textContent = 'No consolidated message history to display.';
                    cell.style.textAlign = 'center';
                    return;
                }

                consolidatedMessages.forEach(msg => {
                    const row = messageHistoryBody.insertRow();
                    let cell;
                    cell = row.insertCell(); cell.textContent = new Date(msg.startTimestamp).toLocaleString(); cell.classList.add('col-timestamp');
                    cell = row.insertCell(); cell.textContent = msg.endTimestamp ? new Date(msg.endTimestamp).toLocaleString() : (msg.status === "Request In Progress" ? "In Progress" : "N/A"); cell.classList.add('col-timestamp');
                    cell = row.insertCell(); cell.textContent = msg.requestId; cell.classList.add('col-request-id');
                    
                    cell = createPreCell(msg.fromClient); cell.classList.add('col-data', 'cell-fromClient'); row.appendChild(cell);
                    cell = createPreCell(msg.toExtension); cell.classList.add('col-data', 'cell-toExtension'); row.appendChild(cell);
                    cell = createPreCell(msg.fromExtension); cell.classList.add('col-data', 'cell-fromExtension'); row.appendChild(cell);
                    cell = createPreCell(msg.toClient); cell.classList.add('col-data', 'cell-toClient'); row.appendChild(cell);
                    
                    cell = row.insertCell(); cell.textContent = msg.status; cell.classList.add('col-status');
                });
            } catch (error) {
                console.error('Error fetching message history:', error);
                messageHistoryBody.innerHTML = '';
                const row = messageHistoryBody.insertRow();
                const cell = row.insertCell();
                cell.colSpan = 7;
                cell.textContent = `Error loading message history: ${error.message}`;
                cell.style.color = 'red';
                cell.style.textAlign = 'center';
            }
        }

        if (refreshButton) {
            refreshButton.addEventListener('click', fetchAndDisplayMessageHistory);
        }

        fetchAndDisplayMessageHistory(); // Initial load for messages
        fetchAndDisplayServerInfo(); // Initial load for server info (status) on the messages tab

        // Elements for settings and status
        const portInputEl = document.getElementById('port-input'); // Corrected ID
        const requestTimeoutInputEl = document.getElementById('request-timeout-input');
        const newRequestBehaviorQueueEl = document.getElementById('newRequestBehaviorQueue');
        const newRequestBehaviorDropEl = document.getElementById('newRequestBehaviorDrop');
        const autoKillPortInputEl = document.getElementById('auto-kill-port-input');
        const saveSettingsBtn = document.getElementById('save-settings-btn');
        const updateStatusMsgEl = document.getElementById('update-status-msg');
        const settingPingIntervalEl = document.getElementById('setting-ping-interval-input');
        // References for status elements (declared once)
        const statusUptimeEl = document.getElementById('status-uptime');
        const statusConnectedExtensionsEl = document.getElementById('status-connected-extensions');
        const restartServerBtn = document.getElementById('restart-server-btn');

        function formatUptime(totalSeconds) {
            if (totalSeconds === null || totalSeconds === undefined) return 'N/A';
            const days = Math.floor(totalSeconds / (3600 * 24));
            totalSeconds %= (3600 * 24);
            const hours = Math.floor(totalSeconds / 3600);
            totalSeconds %= 3600;
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;

            let uptimeString = '';
            if (days > 0) uptimeString += `${days}d `;
            if (hours > 0) uptimeString += `${hours}h `;
            if (minutes > 0) uptimeString += `${minutes}m `;
            uptimeString += `${seconds}s`;
            return uptimeString.trim() || '0s';
        }

        async function fetchAndDisplayServerInfo() {
            try {
                // UPDATED: Fetch from the new consolidated settings endpoint
                const response = await fetch('/admin/settings');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const currentSettings = await response.json();

                console.log('fetchAndDisplayServerInfo received settings:', currentSettings);
                
                // Populate Settings using the new response structure
                if(portInputEl) portInputEl.value = currentSettings.serverPort || '';
                if(requestTimeoutInputEl) requestTimeoutInputEl.value = currentSettings.requestTimeout !== null ? currentSettings.requestTimeout : '';
                // Ping interval is not part of /admin/settings, assuming it's static or handled elsewhere if needed
                // if(settingPingIntervalEl) settingPingIntervalEl.textContent = currentSettings.pingIntervalMs !== null ? `${currentSettings.pingIntervalMs} ms` : 'N/A (Not Implemented)';
                
                if (currentSettings.messageSendStrategy === 'drop') {
                    if(newRequestBehaviorDropEl) newRequestBehaviorDropEl.checked = true;
                } else {
                    if(newRequestBehaviorQueueEl) newRequestBehaviorQueueEl.checked = true; // Default to queue
                }
                
                if (autoKillPortInputEl) {
                    console.log('fetchAndDisplayServerInfo: Setting autoKillPort checkbox to:', currentSettings.autoKillPort);
                    autoKillPortInputEl.checked = currentSettings.autoKillPort === true;
                }

                // Populate Status - Assuming server-info might still be used for uptime/connections or these are separate
                // For now, focusing on settings. If status also needs to come from /admin/settings, that's another change.
                // Let's assume uptime and connectedExtensionsCount are fetched separately or are less critical for this immediate fix.
                // For simplicity, I'll comment out the status population from this specific fetch if it was tied to the old server-info structure.
                // If your /admin/settings also returns uptime and connectedExtensionsCount, uncomment and adjust field names.
                /*
                if(statusUptimeEl) statusUptimeEl.textContent = formatUptime(currentSettings.uptimeSeconds);
                if(statusConnectedExtensionsEl) statusConnectedExtensionsEl.textContent = currentSettings.connectedExtensionsCount !== null ? currentSettings.connectedExtensionsCount : 'N/A';
                */
                // If you still have a /v1/admin/server-info for uptime and connections, that part can remain as is.
                // This function will now primarily handle settings population.
                // To keep status functional if it's from a different endpoint, we might need to separate concerns
                // or ensure /admin/settings returns everything. For now, focusing on fixing settings load.

                // Fetch separate status info if needed (example if /v1/admin/server-info is still used for status)
                const statusResponse = await fetch('/v1/admin/server-info');
                if (statusResponse.ok) {
                    const serverInfoForStatus = await statusResponse.json();
                    if(statusUptimeEl) statusUptimeEl.textContent = formatUptime(serverInfoForStatus.uptimeSeconds);
                    if(statusConnectedExtensionsEl) statusConnectedExtensionsEl.textContent = serverInfoForStatus.connectedExtensionsCount !== null ? serverInfoForStatus.connectedExtensionsCount : 'N/A';
                    if(settingPingIntervalEl && serverInfoForStatus.pingIntervalMs !== undefined) {
                        settingPingIntervalEl.value = serverInfoForStatus.pingIntervalMs !== null ? `${serverInfoForStatus.pingIntervalMs}` : 'Disabled';
                    } else if(settingPingIntervalEl) {
                        settingPingIntervalEl.value = 'Disabled';
                    }
                } else {
                    // Handle case where statusResponse itself is not ok
                    console.warn(`Failed to fetch status from /v1/admin/server-info: ${statusResponse.status}`);
                    if(statusUptimeEl) statusUptimeEl.textContent = 'Status N/A';
                    if(statusConnectedExtensionsEl) statusConnectedExtensionsEl.textContent = 'Status N/A';
                    if(settingPingIntervalEl) settingPingIntervalEl.value = 'Disabled';
                }
            } catch (error) { // This is the single catch block for the try starting at line 310
                console.error('Error in fetchAndDisplayServerInfo:', error);
                // Set all relevant fields to an error state
                if(portInputEl) portInputEl.value = 'Error';
                if(requestTimeoutInputEl) requestTimeoutInputEl.value = 'Error';
                if(settingPingIntervalEl) settingPingIntervalEl.value = 'Disabled';
                if(newRequestBehaviorQueueEl) newRequestBehaviorQueueEl.checked = true; // Default on error
                if(statusUptimeEl) statusUptimeEl.textContent = 'Error loading';
                if(statusConnectedExtensionsEl) statusConnectedExtensionsEl.textContent = 'Error loading';
            }
        }
        
        async function handleSaveSettings() {
            if (!requestTimeoutInputEl || !portInputEl || !updateStatusMsgEl || !newRequestBehaviorQueueEl || !newRequestBehaviorDropEl) return;

            const newTimeout = parseInt(requestTimeoutInputEl.value, 10);
            const newPort = parseInt(portInputEl.value, 10);
            const selectedNewRequestBehavior = newRequestBehaviorQueueEl.checked ? 'queue' : 'drop';
            let settingsToUpdate = {};
            let validationError = false;
            let messages = [];

            if (requestTimeoutInputEl.value.trim() !== '') { // Only process if there's input
                if (!isNaN(newTimeout) && newTimeout > 0) {
                    // UPDATED: Key name for server
                    settingsToUpdate.requestTimeout = newTimeout;
                } else {
                    messages.push('Invalid timeout: Must be a positive number.');
                    validationError = true;
                }
            }

            if (portInputEl.value.trim() !== '') { // Only process if there's input
                 if (!isNaN(newPort) && newPort > 0 && newPort <= 65535) {
                    // UPDATED: Key name for server
                    settingsToUpdate.serverPort = newPort;
                } else {
                    messages.push('Invalid port: Must be between 1 and 65535.');
                    validationError = true;
                }
            }

            // Always include newRequestBehavior as it's controlled by radio buttons
            // No specific validation needed here as it's either 'queue' or 'drop'
            // UPDATED: Key name for server
            settingsToUpdate.messageSendStrategy = selectedNewRequestBehavior;
            
            // Always include autoKillPort as it's controlled by checkbox
            settingsToUpdate.autoKillPort = autoKillPortInputEl ? autoKillPortInputEl.checked : false;
            console.log('Saving autoKillPort setting:', settingsToUpdate.autoKillPort);
            
            if (validationError) {
                updateStatusMsgEl.textContent = messages.join(' ');
                updateStatusMsgEl.style.color = 'red';
                setTimeout(() => { updateStatusMsgEl.textContent = ''; }, 7000);
                return;
            }

            if (Object.keys(settingsToUpdate).length === 0) {
                updateStatusMsgEl.textContent = 'No changes to save.';
                updateStatusMsgEl.style.color = 'blue';
                setTimeout(() => { updateStatusMsgEl.textContent = ''; }, 5000);
                return;
            }

            updateStatusMsgEl.textContent = 'Saving settings...';
            updateStatusMsgEl.style.color = 'orange';

            try {
                console.log('Sending settings to server:', settingsToUpdate);
                // UPDATED: Endpoint for saving settings
                const response = await fetch('/admin/settings', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(settingsToUpdate)
                });
                const result = await response.json();
                console.log('Server response:', result);
                console.log('Server currentSettings:', result.currentSettings);
                if (result.currentSettings) {
                    console.log('Server returned autoKillPort:', result.currentSettings.autoKillPort);
                }
                if (response.ok) {
                    updateStatusMsgEl.textContent = result.message || 'Settings updated successfully!';
                    updateStatusMsgEl.style.color = 'green';
                    // The backend message already indicates if restart is needed for port
                    fetchAndDisplayServerInfo();
                } else {
                    updateStatusMsgEl.textContent = `Error: ${result.error || 'Failed to update settings.'}`;
                    updateStatusMsgEl.style.color = 'red';
                }
            } catch (error) {
                console.error('Error updating settings:', error);
                updateStatusMsgEl.textContent = 'Failed to send update command.';
                updateStatusMsgEl.style.color = 'red';
            }
            // Keep message longer if it mentions restart
            const clearTime = updateStatusMsgEl.textContent.toLowerCase().includes('restart') ? 15000 : 7000;
            setTimeout(() => { updateStatusMsgEl.textContent = ''; }, clearTime);
        }

        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', handleSaveSettings);
        }
        
        async function handleRestartServer() {
            if (confirm('Are you sure you want to restart the server?')) {
                try {
                    const response = await fetch('/v1/admin/restart-server', { method: 'POST' });
                    const result = await response.json();
                    alert(result.message || 'Restart command sent.');
                } catch (error) {
                    console.error('Error restarting server:', error);
                    alert('Failed to send restart command to server.');
                }
            }
        }

        if (restartServerBtn) {
            restartServerBtn.addEventListener('click', handleRestartServer);
        }
        
        // Initial load for settings is handled by tab click or initial messages tab load.
        // fetchAndDisplayServerInfo(); // This is called when messages tab is active or settings tab is clicked.

        // Collapsible column logic
        const columnStates = { // To store the collapsed state
            fromClient: false,
            toExtension: false,
            fromExtension: false,
            toClient: false
        };

        function toggleColumn(columnKey) {
            columnStates[columnKey] = !columnStates[columnKey];
            const isCollapsed = columnStates[columnKey];
            
            // Update button text/indicator
            const button = document.querySelector(`.collapse-btn[data-column="${columnKey}"]`);
            if (button) {
                button.textContent = isCollapsed ? '+' : '-';
                button.title = isCollapsed ? 'Expand Column' : 'Collapse Column';
            }

            // Toggle header visibility (optional, if you want to hide header text too)
            // const headerCell = document.querySelector(`th.col-data[data-column-key="${columnKey}"]`); // Need to add data-column-key to th if this is desired

            // Toggle data cell visibility (these are the <td> elements)
            const cellsToToggle = document.querySelectorAll(`#message-history-body td.cell-${columnKey}`);
            cellsToToggle.forEach(cell => {
                cell.style.display = isCollapsed ? 'none' : '';
            });

            // Special handling for the header cell (<th>) due to table-layout: fixed
            const headerTh = document.querySelector(`th[data-column="${columnKey}"]`);
            if (headerTh) {
                if (isCollapsed) {
                    headerTh.classList.add('header-collapsed');
                } else {
                    headerTh.classList.remove('header-collapsed');
                }
            }
        }

        document.querySelectorAll('.collapse-btn').forEach(button => {
            button.addEventListener('click', function() {
                const columnKey = this.dataset.column;
                toggleColumn(columnKey);
            });
        });
 
        // Initialize settings with default values if not already set
        function initializeDefaultSettings() {
            console.log('Initializing default settings...');
            if (portInputEl && !portInputEl.value) portInputEl.value = '3003';
            if (requestTimeoutInputEl && !requestTimeoutInputEl.value) requestTimeoutInputEl.value = '300000';
            if (settingPingIntervalEl && !settingPingIntervalEl.value) settingPingIntervalEl.value = 'Disabled';
            if (autoKillPortInputEl) {
                console.log('Auto-kill toggle element found, current checked state:', autoKillPortInputEl.checked);
                // Test if the toggle is working by adding a click event listener
                autoKillPortInputEl.addEventListener('change', function() {
                    console.log('Auto-kill toggle changed to:', this.checked);
                });
                
                // Also add click listener to the slider element to ensure it works
                const sliderEl = autoKillPortInputEl.nextElementSibling;
                if (sliderEl && sliderEl.classList.contains('slider')) {
                    sliderEl.addEventListener('click', function() {
                        console.log('Slider clicked, toggling checkbox');
                        autoKillPortInputEl.checked = !autoKillPortInputEl.checked;
                        autoKillPortInputEl.dispatchEvent(new Event('change'));
                    });
                }
            }
            if (newRequestBehaviorQueueEl && !newRequestBehaviorQueueEl.checked && !newRequestBehaviorDropEl.checked) {
                newRequestBehaviorQueueEl.checked = true;
            }
        }
        
        // Initialize default settings
        setTimeout(initializeDefaultSettings, 1000);
        
        // Initialize logs panel
        updateLogsPanel();
        
        // Update logs panel every 2 seconds for real-time logs
        setInterval(updateLogsPanel, 2000);
        
        console.log("Admin UI initialized. Message history, settings, status, restart, and collapsible columns functionality implemented.");
    </script>
</body>
</html>